import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PaginateConfig, PaginateQuery, paginate } from 'nestjs-paginate';
import { PurchaseOrder, PurchaseOrderStatus } from './entities/purchase-order.entity';
import { PurchaseOrderItem } from './entities/purchase-order-item.entity';
import { CreatePurchaseOrderDto } from './dto/create-purchase-order.dto';
import { UpdatePurchaseOrderDto } from './dto/update-purchase-order.dto';
import { PurchaseOrderRepository } from './repository/purchase-order.repository';

export const PURCHASE_ORDER_PAGINATION_CONFIG: PaginateConfig<PurchaseOrder> = {
  sortableColumns: ['id', 'poNumber', 'poDate', 'status', 'supplierName', 'totalAmount', 'createdAt'],
  nullSort: 'last',
  defaultSortBy: [['createdAt', 'DESC']],
  searchableColumns: ['poNumber', 'supplierName', 'supplierContact'],
  select: [
    'id',
    'poNumber', 
    'poDate',
    'expectedDeliveryDate',
    'actualDeliveryDate',
    'status',
    'supplierName',
    'supplierContact',
    'supplierPhone',
    'totalAmount',
    'createdAt',
    'updatedAt',
    'createdBy.id',
    'createdBy.firstName',
    'createdBy.lastName',
    'approvedBy.id',
    'approvedBy.firstName',
    'approvedBy.lastName',
    'branch.id',
    'branch.name'
  ],
  relations: {
    createdBy: true,
    approvedBy: true,
    branch: true
  },
  filterableColumns: {
    status: true,
    'branch.id': true,
    'createdBy.id': true
  },
  defaultLimit: 20,
  maxLimit: 100,
};

@Injectable()
export class PurchaseOrderService {
  constructor(
    @InjectRepository(PurchaseOrder)
    private readonly purchaseOrderRepository: Repository<PurchaseOrder>,
    @InjectRepository(PurchaseOrderItem)
    private readonly purchaseOrderItemRepository: Repository<PurchaseOrderItem>,
    private readonly purchaseOrderRepo: PurchaseOrderRepository,
  ) {}

  async datatables(query: PaginateQuery) {
    return paginate(query, this.purchaseOrderRepository, PURCHASE_ORDER_PAGINATION_CONFIG);
  }

  async findAll() {
    return this.purchaseOrderRepo.findAll();
  }

  async findOne(id: number) {
    const purchaseOrder = await this.purchaseOrderRepo.findOne(id);
    if (!purchaseOrder) {
      throw new NotFoundException(`Purchase Order with ID ${id} not found`);
    }
    return purchaseOrder;
  }

  async findByPoNumber(poNumber: string) {
    const purchaseOrder = await this.purchaseOrderRepo.findByPoNumber(poNumber);
    if (!purchaseOrder) {
      throw new NotFoundException(`Purchase Order with PO Number ${poNumber} not found`);
    }
    return purchaseOrder;
  }

  async create(createPurchaseOrderDto: CreatePurchaseOrderDto, userId: number) {
    // คำนวณยอดรวมจาก items
    const calculatedSubtotal = createPurchaseOrderDto.items.reduce((sum, item) => sum + item.totalPrice, 0);
    const calculatedTaxAmount = calculatedSubtotal * (createPurchaseOrderDto.taxRate || 0) / 100;
    const calculatedTotal = calculatedSubtotal + calculatedTaxAmount - (createPurchaseOrderDto.discountAmount || 0);

    const dataToCreate = {
      ...createPurchaseOrderDto,
      subtotal: calculatedSubtotal,
      taxAmount: calculatedTaxAmount,
      totalAmount: calculatedTotal
    };

    return this.purchaseOrderRepo.create(dataToCreate, userId);
  }

  async update(id: number, updatePurchaseOrderDto: UpdatePurchaseOrderDto) {
    const existingPo = await this.findOne(id);
    
    // ตรวจสอบว่าสามารถแก้ไขได้หรือไม่
    if (existingPo.status === PurchaseOrderStatus.COMPLETED || existingPo.status === PurchaseOrderStatus.CANCELLED) {
      throw new BadRequestException(`Cannot update Purchase Order with status ${existingPo.status}`);
    }

    return this.purchaseOrderRepo.update(id, updatePurchaseOrderDto);
  }

  async remove(id: number) {
    const existingPo = await this.findOne(id);
    
    // ตรวจสอบว่าสามารถลบได้หรือไม่
    if (existingPo.status !== PurchaseOrderStatus.DRAFT) {
      throw new BadRequestException(`Cannot delete Purchase Order with status ${existingPo.status}`);
    }

    return this.purchaseOrderRepo.remove(id);
  }

  async approve(id: number, userId: number) {
    const existingPo = await this.findOne(id);
    
    if (existingPo.status !== PurchaseOrderStatus.PENDING) {
      throw new BadRequestException(`Cannot approve Purchase Order with status ${existingPo.status}`);
    }

    return this.purchaseOrderRepo.update(id, {
      status: PurchaseOrderStatus.APPROVED,
      approvedBy: userId,
      approvedAt: new Date().toISOString()
    });
  }

  async changeStatus(id: number, status: PurchaseOrderStatus) {
    const existingPo = await this.findOne(id);
    
    // ตรวจสอบการเปลี่ยนสถานะที่ถูกต้อง
    const validTransitions = this.getValidStatusTransitions(existingPo.status);
    if (!validTransitions.includes(status)) {
      throw new BadRequestException(`Cannot change status from ${existingPo.status} to ${status}`);
    }

    return this.purchaseOrderRepo.update(id, { status });
  }

  async receiveItems(id: number, receivedItems: { itemId: number; receivedQuantity: number }[]) {
    const purchaseOrder = await this.findOne(id);
    
    if (purchaseOrder.status !== PurchaseOrderStatus.ORDERED) {
      throw new BadRequestException(`Cannot receive items for Purchase Order with status ${purchaseOrder.status}`);
    }

    for (const receivedItem of receivedItems) {
      const item = purchaseOrder.items.find(i => i.id === receivedItem.itemId);
      if (!item) {
        throw new NotFoundException(`Purchase Order Item with ID ${receivedItem.itemId} not found`);
      }

      const newReceivedQuantity = item.receivedQuantity + receivedItem.receivedQuantity;
      if (newReceivedQuantity > item.quantity) {
        throw new BadRequestException(`Received quantity cannot exceed ordered quantity for item ${item.productName}`);
      }

      await this.purchaseOrderItemRepository.update(receivedItem.itemId, {
        receivedQuantity: newReceivedQuantity,
        remainingQuantity: item.quantity - newReceivedQuantity
      });
    }

    // ตรวจสอบว่าได้รับสินค้าครบหรือไม่
    const updatedPo = await this.findOne(id);
    const allItemsReceived = updatedPo.items.every(item => item.remainingQuantity === 0);
    
    if (allItemsReceived) {
      await this.purchaseOrderRepo.update(id, {
        status: PurchaseOrderStatus.RECEIVED,
        actualDeliveryDate: new Date().toISOString()
      });
    }

    return this.findOne(id);
  }

  private getValidStatusTransitions(currentStatus: PurchaseOrderStatus): PurchaseOrderStatus[] {
    const transitions = {
      [PurchaseOrderStatus.DRAFT]: [PurchaseOrderStatus.PENDING, PurchaseOrderStatus.CANCELLED],
      [PurchaseOrderStatus.PENDING]: [PurchaseOrderStatus.APPROVED, PurchaseOrderStatus.CANCELLED],
      [PurchaseOrderStatus.APPROVED]: [PurchaseOrderStatus.ORDERED, PurchaseOrderStatus.CANCELLED],
      [PurchaseOrderStatus.ORDERED]: [PurchaseOrderStatus.RECEIVED, PurchaseOrderStatus.CANCELLED],
      [PurchaseOrderStatus.RECEIVED]: [PurchaseOrderStatus.COMPLETED],
      [PurchaseOrderStatus.COMPLETED]: [],
      [PurchaseOrderStatus.CANCELLED]: []
    };

    return transitions[currentStatus] || [];
  }
}
