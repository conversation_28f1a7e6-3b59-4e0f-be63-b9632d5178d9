import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>oad, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ToMany } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { Exclude, Expose } from "class-transformer";
import { Customer } from "src/customer/entities/customer.entity";

@Entity()
export class Upload extends CustomBaseEntity {

    @Column()
    fieldname: string;

    @Column()
    originalname: string;

    @Column()
    encoding: string;

    @Column()
    mimetype: string;

    @Column()
    destination: string;

    @Column()
    filename: string;

    @Column()
    path: string;

    @Column()
    size: number

    @Column({ default: 'local' })
    provider: string;

    @Expose()
    get pathUrl(): string {
        return this.provider == 'local' ? process.env.APP_URL + '/' + this.filename : this.filename;
    }

    @OneToMany(() => Customer, (_) => _.identityCard)
    customers: Array<Customer>;

    constructor(partial?: Partial<Upload>) {
        super();
        if (partial) {
            Object.assign(this, partial)
        }
    }
}
