import { Column, <PERSON>tity, ManyToOne, OneToMany } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { DecimalColumnTransformer } from "../../common/decimal-column-transformer";
import { Queue } from "./queue.entity";
import { Product } from "../../product/entities/product.entity";
import { Unit } from "src/unit/entities/unit.entity";
import { QueueItemPhoto } from "./queue-item-photo.entity";
import { QueueItemAllowance } from "./queue-item-allowance.entity";

// export enum QueueItemStatus {
//     ACCEPTED = 'accepted',
//     REJECTED = 'rejected',
// }

@Entity()
export class QueueItem extends CustomBaseEntity {
    @ManyToOne(() => Queue, (_) => _.queueItems, { onDelete: 'CASCADE' })
    queue: Queue;

    @ManyToOne(() => Product, (_) => _.queueItems)
    product: Product;

    @ManyToOne(() => Unit, (_) => _.queueItems)
    unit: Unit;

    @Column({ type: 'numeric', nullable: true, transformer: new DecimalColumnTransformer() })
    netWeight: number;

    @Column({ nullable: true })
    note: string;

    @Column('numeric', { nullable: true, transformer: new DecimalColumnTransformer() })
    price: number;

    @Column('numeric', { nullable: true, transformer: new DecimalColumnTransformer() })
    total: number;

    @OneToMany(() => QueueItemPhoto, (_) => _.queueItem, { cascade: true })
    photos: QueueItemPhoto[];

    @Column('boolean', { default: false })
    accepted: boolean;

    @Column({ nullable: true })
    productName: string;

    @Column({ nullable: true })
    unitName: string;

    @Column('numeric', { nullable: true, transformer: new DecimalColumnTransformer() })
    grossWeight: number;

    @Column('numeric', { nullable: true, transformer: new DecimalColumnTransformer() })
    tareWeight: number;

    @Column('numeric', { nullable: true, transformer: new DecimalColumnTransformer() })
    deductedWeight: number;

    @OneToMany(() => QueueItemAllowance, (_) => _.queueItem, { cascade: true })
    allowance: QueueItemAllowance[];

}