import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { CreateQueueDto } from './dto/create-queue.dto';
import { CreateQueueItemArrayDto, CreateQueueItemDto } from './dto/create-queue-item.dto';
import { UpdateQueueDto } from './dto/update-queue.dto';
import { AliveStatus, Queue, QueueStatus } from './entities/queue.entity';
import { Branch } from 'src/branch/entities/branch.entity';
import { DateTime } from 'luxon';
import { In, Like, Raw } from 'typeorm';
import { paginate, PaginateConfig, Paginated, PaginateQuery } from 'nestjs-paginate';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { QueueItem } from './entities/queue-item.entity';
import { Product } from 'src/product/entities/product.entity';
import { Unit } from 'src/unit/entities/unit.entity';
import { UpdateQueueStatusDto } from './dto/update-queue-status.dto';
import { SkipQueueDto } from './dto/skip-queue-note.dto';
import { QueuePhoto, QueuePhotoStatus } from './entities/queue-photo.entity';
import { CarType } from './entities/car-type.entity';
import { I18nContext, I18nService } from 'nestjs-i18n';
import { OutQueueDto } from './dto/out-queue.dto';
import { QueueItemPhoto } from './entities/queue-item-photo.entity';
import { QueueItemAllowance } from './entities/queue-item-allowance.entity';

export const QUEUE_PAGINATION_CONFIG: PaginateConfig<Queue> = {
    sortableColumns: ['id', 'queueNo', 'customerName', 'vehicleNumber'],
    searchableColumns: ['queueNo', 'customerName', 'vehicleNumber'],
    filterableColumns: {
        type: true,
        status: true,
    },
    relativePath: true,
};

@Injectable()
export class QueueService {

    constructor(
        @InjectRepository(Queue)
        private queueRepository: Repository<Queue>,

        @InjectRepository(Branch)
        private branchRepository: Repository<Branch>,

        private readonly i18n: I18nService
    ) { }


    async createQueue(createQueueDto: CreateQueueDto, userId: number) {
        const today = DateTime.local();
        const todayStr = today.toFormat('yyyy-MM-dd'); // YYYY-MM-DD

        const queueNo = await this.generateQueueNumber();

        const queue = Queue.create({
            queueNo: queueNo,
            idCard: createQueueDto.idCard,
            customerName: createQueueDto.customerName,
            customerAddress: createQueueDto.customerAddress,
            phoneNumber: createQueueDto.phoneNumber,
            vehicleNumber: createQueueDto.vehicleNumber,
            queueNumber: createQueueDto.visitorNo,
            queueDate: todayStr,
            arrivalTime: today.toFormat('HH:mm:ss'),
            status: QueueStatus.WAITING,
            branch: { id: createQueueDto.branchId },
            customerCompany: createQueueDto.customerCompany,
            department: createQueueDto.department,
            followerCount: createQueueDto.followerCount,
            coverPhoto: createQueueDto.coverPhoto,
            carType: { id: createQueueDto.carTypeId },
            otherCarType: createQueueDto.otherCarType,
            createdBy: { id: userId },
            purpose: createQueueDto.purpose,
            otherPurposeDetail: createQueueDto.otherPurposeDetail,
            productType: createQueueDto.productType,
            entranceWeight: createQueueDto.entranceWeight,
            entranceTime: createQueueDto.entranceTime,
        });

        try {
            await queue.save();
        } catch (error) {
            throw new BadRequestException(this.i18n.t('queue.SAVE_FAILED'));
        }

        // ถ้าเพิ่มรูปเข้ามา
        if (createQueueDto.photos && createQueueDto.photos.length > 0) {
            const photosToSave: QueuePhoto[] = [];
            for (const photoInfo of createQueueDto.photos) {
                const newPhoto = QueuePhoto.create({
                    path: photoInfo.path,
                    status: QueuePhotoStatus.INCOME,
                    queue: queue
                });
                photosToSave.push(newPhoto);

            }
            try {
                await QueuePhoto.save(photosToSave);
            }
            catch (error) {
                throw new BadRequestException(this.i18n.t('queue.PHOTO_SAVE_FAILED'));
            }
        }

        return queue;
    }


    // แก้ไขข้อมูล คิว
    async updateQueue(id: number, updateQueueDto: UpdateQueueDto) {
        const queue = await Queue.exists({ where: { id } });
        if (!queue) {
            throw new NotFoundException(this.i18n.t('queue.NOT_FOUND'));
        }

        const branch = await Branch.findOne({ where: { id: updateQueueDto.branchId } });
        if (!branch) {
            throw new NotFoundException(this.i18n.t('branch.NOT_FOUND'));
        }

        let carType = null;
        if (updateQueueDto.carTypeId) {
            carType = await CarType.findOne({ where: { id: updateQueueDto.carTypeId } });
            if (!carType) {
                throw new NotFoundException(this.i18n.t('car-type.NOT_FOUND'));
            }
        }

        await Queue.update(id, {
            idCard: updateQueueDto.idCard,
            customerName: updateQueueDto.customerName,
            phoneNumber: updateQueueDto.phoneNumber,
            vehicleNumber: updateQueueDto.vehicleNumber,
            customerCompany: updateQueueDto.customerCompany,
            department: updateQueueDto.department,
            carType: {
                id: updateQueueDto.carTypeId
            },
            otherCarType: updateQueueDto.otherCarType,
            entranceWeight: updateQueueDto.weight1,
            entranceTime: updateQueueDto.weight1Time,
            exitWeight: updateQueueDto.weight2,
            exitTime: updateQueueDto.weight2Time,
            branch: branch,
            queueNumber: updateQueueDto.visitorNo,
            purpose: updateQueueDto.purpose,
            otherPurposeDetail: updateQueueDto.otherPurposeDetail,
            productType: updateQueueDto.productType,
            doneTime: updateQueueDto.doneTime,
            status: updateQueueDto.status
        });

        // return await Queue.save(queue);
        // const savedQueue = await Queue.save(queue);

        if (updateQueueDto.photos && updateQueueDto.photos.length > 0) {
            // ลบรูปเก่าทั้งหมด
            await QueuePhoto.delete({ queue: { id } });

            const photosToSave: QueuePhoto[] = [];
            for (const photoInfo of updateQueueDto.photos) {
                const newPhoto = QueuePhoto.create({
                    path: photoInfo.path,
                    status: photoInfo.status,
                    queue: { id },
                });
                photosToSave.push(newPhoto);
            }

            try {
                await QueuePhoto.save(photosToSave);
            } catch (error) {
                throw new NotFoundException(this.i18n.t('queue.PHOTO_UPDATE_FAILED'));
            }
        }

        return await Queue.findOne({
            where: { id },
            relations: {
                branch: true,
                queueItems: { product: true, unit: true },
                photos: true,
                createdBy: true,
                servedBy: true,
                officer: true,
                carType: true,
            },
        });
    }

    async getAllCarType() {
        return await CarType.find();
    }


    // แสดงคิวที่ทั้งหมดตามสถานะ
    async getAllQueue(status?: QueueStatus[], queueDate?: string) {
        const where: any = {};

        if (queueDate) {
            where.queueDate = queueDate;
        }
        if (Array.isArray(status)) { // ถ้าไม่มี status ก็จะ filter แค่วันที่
            const validStatus = status.filter(s => s && s.trim() !== '');
            if (validStatus.length > 0) {
                where.status = In(validStatus);
            }
        }

        const [queue, count] = await Queue.findAndCount({
            where,
            relations: {
                branch: true,
                queueItems: { product: true, unit: true },
                photos: true,
                createdBy: true,
                servedBy: true,
                officer: true,
                carType: true,
            },
            order: { queueNumber: 'ASC' },
        });

        return { count, queue };
    }


    // แสดงคิวที่หาย ทั้งหมดแยกตามสถานะ
    async getAllQueueMissing(status: string, queueDate: string) {
        const where: any = { alive: false };

        if (queueDate) {
            where.queueDate = queueDate;
        }
        if (status && status !== "") {
            where.status = status;
        }

        const [queue, count] = await Queue.findAndCount({
            where,
            relations: {
                branch: true,
                queueItems: { product: true, unit: true },
                photos: true,
                createdBy: true,
                servedBy: true,
                officer: true,
                carType: true,
            },
            order: { queueNumber: 'ASC' },
        });

        return { count, queue };
    }

    // แสดงคิวตาม id
    async findOneQueue(id: number) {
        const queue = await Queue.findOne({
            where: { id },
            relations: {
                branch: true,
                queueItems: { product: true, unit: true, photos: true, allowance: true },
                photos: true,
                createdBy: true,
                servedBy: true,
                officer: true,
                carType: true,
            },
        });

        if (!queue) throw new NotFoundException(this.i18n.t('queue.NOT_FOUND'));

        return queue
    }


    // กรอกน้ำหนักเปลี่ยน status
    async updateQueueStatusToWeighing(id: number, updateQueueStatus: UpdateQueueStatusDto, userId: number) {
        const queue = await Queue.findOne({ where: { id } })
        if (!queue) throw new NotFoundException(this.i18n.t('queue.NOT_FOUND'));
        // ถ้ากรอกน้ำหนักแสดงว่า ถึง queue แล้ว เปลี่ยน status
        if ((updateQueueStatus.weight1 !== undefined && updateQueueStatus.weight1 !== null) ||
            (updateQueueStatus.weight2 !== undefined && updateQueueStatus.weight2 !== null)) {

            await Queue.update(id, {
                entranceWeight: updateQueueStatus.weight1,
                entranceTime: updateQueueStatus.weight1Time,
                exitWeight: updateQueueStatus.weight2,
                exitTime: updateQueueStatus.weight2Time,
                status: QueueStatus.WEIGHED, // เปลี่ยนเป็น WEIGHING
                alive: true,
                servedBy: { id: userId }, // พนักงานที่ชั่งน้ำหนัก
            })
        }
        // return Queue.save(queue);
        // const savedQueue = await Queue.save(queue);

        if (updateQueueStatus.photos && updateQueueStatus.photos.length > 0) {
            const photosToSave: QueuePhoto[] = [];
            for (const photoInfo of updateQueueStatus.photos) {
                const newPhoto = QueuePhoto.create({
                    path: photoInfo.path,
                    status: QueuePhotoStatus.WEIGHT, // เป็นรูปตอนชั่งน้ำหนัก
                    queue: { id },
                });
                photosToSave.push(newPhoto);

            }
            try {
                await QueuePhoto.save(photosToSave);
            }
            catch (error) {
                throw new NotFoundException(this.i18n.t('queue.PHOTO_SAVE_FAILED'));
            }
        }

        return await Queue.findOne({
            where: { id },
            relations: {
                branch: true,
                queueItems: { product: true, unit: true },
                photos: true,
                createdBy: true,
                servedBy: true,
                officer: true,
                carType: true,
            },
        });
    }

    // ข้ามคิว
    async skipQueue(id: number, skipQueue: SkipQueueDto) {
        const queue = await Queue.exists({ where: { id } })
        if (!queue) throw new NotFoundException(this.i18n.t('queue.NOT_FOUND'));

        await Queue.update(id, {
            skipNote: skipQueue.skipNote,
            alive: skipQueue.alive
        });

        return await Queue.findOne({
            where: { id },
            relations: {
                branch: true,
                queueItems: { product: true, unit: true },
                photos: true,
                createdBy: true,
                servedBy: true,
                officer: true,
                carType: true,
            },
        });
    }

    async remove(id: number) {
        const queue = await Queue.findOne({ where: { id } });
        if (!queue) throw new NotFoundException(this.i18n.t('queue.NOT_FOUND'));
        await Queue.delete(id);
        return queue;
    }


    // คิวนี้มี สินค้า อะไรบ้าง
    async addQueueItems(id: number, createQueueItemArrayDto: CreateQueueItemArrayDto) {
        const queue = await Queue.findOne({ where: { id } });
        if (!queue) throw new NotFoundException(this.i18n.t('queue.NOT_FOUND'));

        const isExists = await QueueItem.find({
            where: { queue: { id } }
        });

        // ถ้ามี queueItem อยู่แล้วแสดงว่าเป็นการ update ให้ลบ queueItem อันเก่าออกก่อน
        if (isExists && isExists.length > 0) {
            await QueueItem.delete({ queue: { id } });
            queue.total = 0;
            await Queue.save(queue);
        }

        const queueItemsToSave: QueueItem[] = [];
        const photosToSave: QueuePhoto[] = [];
        let grandTotal = 0;

        // บันทึก QueueItems
        for (const item of createQueueItemArrayDto.queueItems) {
            const product = await Product.findOne({ where: { id: item.product } });
            if (!product) throw new NotFoundException(this.i18n.t('product.NOT_FOUND'));

            const unit = await Unit.findOne({ where: { id: item.unit } });
            if (!unit) throw new NotFoundException(this.i18n.t('unit.NOT_FOUND'));

            // const total = item.netWeight * item.price;
            const itemPhotos = (item.photos || []).map((photo) => QueueItemPhoto.create({ path: photo.path, step: photo.step }));
            const itemAllowance = (item.allowance || []).map(alw => QueueItemAllowance.create({ name: alw.name, weight: alw.weight }));

            const allowanceWeight = itemAllowance.reduce((sum, alw) => sum + alw.weight, 0); // รวมน้ำหนักของ allowance
            const calNetWeight = item.grossWeight - item.tareWeight - item.deductedWeight - allowanceWeight;
            const calTotal = calNetWeight * item.price;

            // if (calTotal != item.total) {
            //     throw new BadRequestException("The submitted total does not match the calculated total");
            // }

            const queueItem = QueueItem.create({
                queue: { id: id },
                note: item.note,
                product: { id: item.product },
                productName: item.productName,
                unit: { id: item.unit },
                unitName: item.unitName,
                grossWeight: item.grossWeight,
                tareWeight: item.tareWeight,
                deductedWeight: item.deductedWeight,
                allowance: itemAllowance,
                netWeight: item.netWeight,
                price: item.price,
                total: item.total,
                photos: itemPhotos,
                accepted: item.accepted,
            });

            // เก็บ QueueItem ใน array ก่อน
            queueItemsToSave.push(queueItem);

            grandTotal += item.total;
        }
        // บันทึกทีเดียว
        const savedQueueItem = await QueueItem.save(queueItemsToSave);

        if (createQueueItemArrayDto.queuePhotos && createQueueItemArrayDto.queuePhotos.length > 0) {
            for (const photoInfo of createQueueItemArrayDto.queuePhotos) {
                const newPhoto = QueuePhoto.create({
                    path: photoInfo.path,
                    status: QueuePhotoStatus.SORT,
                    queue: { id: queue.id }
                });
                photosToSave.push(newPhoto);
            }

            try {
                await QueuePhoto.save(photosToSave);
            } catch (error) {
                throw new NotFoundException(this.i18n.t('queue.PHOTO_SAVE_FAILED'));
            }
        }

        queue.status = createQueueItemArrayDto?.status ?? queue.status;
        queue.total = grandTotal;
        await Queue.save(queue);

        return savedQueueItem;
    }


    async outQueue(id: number, outQueueDto: OutQueueDto) {
        const queue = await Queue.exists({ where: { id } });
        if (!queue) throw new NotFoundException(this.i18n.t('queue.NOT_FOUND'));

        await Queue.update(id, {
            doneTime: outQueueDto.doneTime,
            status: QueueStatus.DONE,
            exitWeight: outQueueDto.exitWeight,
            exitTime: outQueueDto.exitTime,
        })

        return await Queue.findOne({
            where: { id },
            relations: {
                branch: true,
                queueItems: { product: true, unit: true },
                photos: true,
                createdBy: true,
                servedBy: true,
                officer: true,
                carType: true,
            },
        });
    }

    async runQueueNumber() {
        const today = DateTime.local();
        const todayStr = today.toFormat('yyyy-MM-dd');

        const lastQueueToday = await Queue.findOne({
            where: {
                queueDate: Raw(alias => `DATE(${alias}) = '${todayStr}'`),
            },
            order: { queueNumber: 'DESC' },
        });

        const nextQueueNumber = lastQueueToday ? lastQueueToday.queueNumber + 1 : 1;

        return { queueNumber: nextQueueNumber };
    }

    async callNextQueue(): Promise<Queue | null> {
        const today = DateTime.local();
        const todayStr = today.toFormat('yyyy-MM-dd'); // YYYY-MM-DD
        const thaiDate = today.toFormat('yyMMdd'); //250515

        const nextQueue = await Queue.findOne({
            where: {
                status: QueueStatus.WAITING,
                queueDate: Raw(date => `DATE(${date}) = '${todayStr}'`),
            },
            order: { arrivalTime: 'ASC' },
        });

        if (nextQueue) {
            nextQueue.status = QueueStatus.WEIGHED;
            return Queue.save(nextQueue);
        }

        return null;
    }


    async finishQueue(id: number) {
        const queue = await Queue.findOne({ where: { id } });
        if (!queue) throw new NotFoundException(this.i18n.t('queue.NOT_FOUND'));

        queue.status = QueueStatus.DONE;
        return Queue.save(queue);
    }

    async cancelQueue(id: number) {
        const queue = await Queue.findOne({ where: { id } });
        if (!queue) throw new NotFoundException(this.i18n.t('queue.NOT_FOUND'));

        queue.status = QueueStatus.CANCELLED;
        return Queue.save(queue);
    }

    async datatables(query: PaginateQuery): Promise<Paginated<Queue>> {
        return paginate(query, Queue.getRepository(), QUEUE_PAGINATION_CONFIG);
    }

    async generateQueueNumber(): Promise<string> {
        const today = DateTime.local();
        const todayStr = today.toFormat('yyyy-MM-dd'); // YYYY-MM-DD
        const thaiYear = today.year + 543;
        const thaiDate = today.set({ year: thaiYear }).toFormat('yyMMdd'); // ปี พ.ศ. 2 หลัก + MMdd
        const prefix = 'Q' + thaiDate; // QYYMMDD
        let runNo = '00001'; // 5 หลัก
        let newQueueNo: string;

        const lastQueueToday = await Queue.find({
            where: {
                queueNo: Like(`${prefix}%`),
                queueDate: Raw(date => `DATE(${date}) = '${todayStr}'`),
            },
            order: { queueNo: 'DESC' },
            take: 1,
        });

        if (lastQueueToday.length > 0) {
            const lastQueue = lastQueueToday[0].queueNo;
            const lastRunNumberStr = lastQueue.slice(7); // ดึงเลข 5 หลักหลัง QYYMMDD
            const lastRunNumber = parseInt(lastRunNumberStr);
            const nextRunNumber = lastRunNumber + 1;
            runNo = nextRunNumber.toString().padStart(5, '0');
        }

        newQueueNo = prefix + runNo;
        return newQueueNo;
    }
}
