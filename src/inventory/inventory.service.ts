import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { Inventory } from './entities/inventory.entity';
import { InventoryTransaction, TransactionType } from './entities/inventory-transaction.entity';
import { CreateInventoryDto } from './dto/create-inventory.dto';
import { UpdateInventoryDto } from './dto/update-inventory.dto';
import { InventoryAdjustmentDto } from './dto/inventory-adjustment.dto';

@Injectable()
export class InventoryService {
  constructor(
    @InjectRepository(Inventory)
    private inventoryRepository: Repository<Inventory>,
    @InjectRepository(InventoryTransaction)
    private transactionRepository: Repository<InventoryTransaction>,
    private dataSource: DataSource,
  ) {}

  async create(createInventoryDto: CreateInventoryDto): Promise<Inventory> {
    // ตรวจสอบว่ามี inventory สำหรับ product, branch, warehouse นี้แล้วหรือไม่
    const existingInventory = await this.inventoryRepository.findOne({
      where: {
        product: { id: createInventoryDto.productId },
        branch: { id: createInventoryDto.branchId },
        warehouse: { id: createInventoryDto.warehouseId }
      }
    });

    if (existingInventory) {
      throw new BadRequestException('Inventory already exists for this product, branch, and warehouse combination');
    }

    const inventory = this.inventoryRepository.create({
      product: { id: createInventoryDto.productId } as any,
      branch: { id: createInventoryDto.branchId } as any,
      warehouse: { id: createInventoryDto.warehouseId } as any,
      quantity: createInventoryDto.quantity || 0,
      reservedQuantity: 0,
      availableQuantity: createInventoryDto.quantity || 0,
      minStock: createInventoryDto.minStock || 0,
      maxStock: createInventoryDto.maxStock || 0,
      reorderPoint: createInventoryDto.reorderPoint || 0,
      averageCost: createInventoryDto.averageCost || 0,
      notes: createInventoryDto.notes,
      lastTransactionDate: new Date()
    });

    const savedInventory = await this.inventoryRepository.save(inventory);

    // สร้าง transaction เริ่มต้น
    if (createInventoryDto.quantity && createInventoryDto.quantity > 0) {
      await this.createTransaction({
        inventoryId: savedInventory.id,
        type: TransactionType.IN,
        quantity: createInventoryDto.quantity,
        unitCost: createInventoryDto.averageCost,
        description: 'Initial inventory setup',
        referenceType: 'initial_setup'
      }, 1); // สมมติ userId = 1
    }

    return this.findOne(savedInventory.id);
  }

  async findAll(): Promise<Inventory[]> {
    return this.inventoryRepository.find({
      relations: {
        product: {
          category: true,
          unit: true
        },
        branch: true,
        warehouse: true
      },
      order: {
        createdAt: 'DESC'
      }
    });
  }

  async findByBranch(branchId: number): Promise<Inventory[]> {
    return this.inventoryRepository.find({
      where: {
        branch: { id: branchId }
      },
      relations: {
        product: {
          category: true,
          unit: true
        },
        branch: true,
        warehouse: true
      },
      order: {
        warehouse: { name: 'ASC' },
        product: { name: 'ASC' }
      }
    });
  }

  async findByWarehouse(warehouseId: number): Promise<Inventory[]> {
    return this.inventoryRepository.find({
      where: {
        warehouse: { id: warehouseId }
      },
      relations: {
        product: {
          category: true,
          unit: true
        },
        branch: true,
        warehouse: true
      },
      order: {
        product: { name: 'ASC' }
      }
    });
  }

  async findByProduct(productId: number): Promise<Inventory[]> {
    return this.inventoryRepository.find({
      where: {
        product: { id: productId }
      },
      relations: {
        product: {
          category: true,
          unit: true
        },
        branch: true,
        warehouse: true
      },
      order: {
        branch: { name: 'ASC' },
        warehouse: { name: 'ASC' }
      }
    });
  }

  async findOne(id: number): Promise<Inventory> {
    const inventory = await this.inventoryRepository.findOne({
      where: { id },
      relations: {
        product: {
          category: true,
          unit: true
        },
        branch: true,
        warehouse: true,
        transactions: {
          createdBy: true
        }
      }
    });

    if (!inventory) {
      throw new NotFoundException(`Inventory with ID ${id} not found`);
    }

    return inventory;
  }

  async findByProductAndLocation(productId: number, branchId: number, warehouseId: number): Promise<Inventory> {
    const inventory = await this.inventoryRepository.findOne({
      where: {
        product: { id: productId },
        branch: { id: branchId },
        warehouse: { id: warehouseId }
      },
      relations: {
        product: true,
        branch: true,
        warehouse: true
      }
    });

    if (!inventory) {
      throw new NotFoundException(`Inventory not found for product ${productId} in branch ${branchId}, warehouse ${warehouseId}`);
    }

    return inventory;
  }

  async update(id: number, updateInventoryDto: UpdateInventoryDto): Promise<Inventory> {
    const inventory = await this.findOne(id);

    const updateData: any = { ...updateInventoryDto };
    
    // ลบ foreign key fields ออกจาก updateData
    delete updateData.productId;
    delete updateData.branchId;
    delete updateData.warehouseId;

    await this.inventoryRepository.update(id, updateData);
    return this.findOne(id);
  }

  async remove(id: number): Promise<void> {
    const inventory = await this.findOne(id);

    // ตรวจสอบว่ามี quantity คงเหลือหรือไม่
    if (inventory.quantity > 0) {
      throw new BadRequestException('Cannot delete inventory with remaining quantity');
    }

    await this.inventoryRepository.softDelete(id);
  }

  async createTransaction(adjustmentDto: InventoryAdjustmentDto, userId: number): Promise<InventoryTransaction> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const inventory = await queryRunner.manager.findOne(Inventory, {
        where: { id: adjustmentDto.inventoryId },
        lock: { mode: 'pessimistic_write' }
      });

      if (!inventory) {
        throw new NotFoundException(`Inventory with ID ${adjustmentDto.inventoryId} not found`);
      }

      // คำนวณยอดใหม่
      const newQuantity = inventory.quantity + adjustmentDto.quantity;
      
      if (newQuantity < 0) {
        throw new BadRequestException('Insufficient inventory quantity');
      }

      // อัปเดต inventory
      const newAvailableQuantity = newQuantity - inventory.reservedQuantity;
      await queryRunner.manager.update(Inventory, adjustmentDto.inventoryId, {
        quantity: newQuantity,
        availableQuantity: newAvailableQuantity,
        lastTransactionDate: new Date(),
        lastUpdatedBy: userId
      });

      // สร้าง transaction
      const transaction = queryRunner.manager.create(InventoryTransaction, {
        inventory: { id: adjustmentDto.inventoryId } as any,
        type: adjustmentDto.type,
        quantity: adjustmentDto.quantity,
        balanceAfter: newQuantity,
        unitCost: adjustmentDto.unitCost,
        totalCost: adjustmentDto.unitCost ? adjustmentDto.unitCost * Math.abs(adjustmentDto.quantity) : null,
        referenceType: adjustmentDto.referenceType,
        referenceId: adjustmentDto.referenceId,
        referenceNumber: adjustmentDto.referenceNumber,
        transactionDate: new Date(),
        description: adjustmentDto.description,
        notes: adjustmentDto.notes,
        createdBy: { id: userId } as any
      });

      const savedTransaction = await queryRunner.manager.save(transaction);

      await queryRunner.commitTransaction();
      return savedTransaction;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async getTransactionHistory(inventoryId: number): Promise<InventoryTransaction[]> {
    return this.transactionRepository.find({
      where: {
        inventory: { id: inventoryId }
      },
      relations: {
        createdBy: true
      },
      order: {
        createdAt: 'DESC'
      }
    });
  }

  async getLowStockItems(branchId?: number): Promise<Inventory[]> {
    const queryBuilder = this.inventoryRepository.createQueryBuilder('inventory')
      .leftJoinAndSelect('inventory.product', 'product')
      .leftJoinAndSelect('inventory.branch', 'branch')
      .leftJoinAndSelect('inventory.warehouse', 'warehouse')
      .where('inventory.quantity <= inventory.reorderPoint')
      .andWhere('inventory.reorderPoint > 0');

    if (branchId) {
      queryBuilder.andWhere('inventory.branchId = :branchId', { branchId });
    }

    return queryBuilder.getMany();
  }
}
