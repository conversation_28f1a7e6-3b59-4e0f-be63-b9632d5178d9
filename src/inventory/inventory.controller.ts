import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  ParseIntPipe,
  Query,
  Request,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { InventoryService } from './inventory.service';
import { CreateInventoryDto } from './dto/create-inventory.dto';
import { UpdateInventoryDto } from './dto/update-inventory.dto';
import { InventoryAdjustmentDto } from './dto/inventory-adjustment.dto';
import { Inventory } from './entities/inventory.entity';
import { InventoryTransaction } from './entities/inventory-transaction.entity';
import { Auth } from '../auth/decorators/auth.decorator';

@ApiTags('Inventory')
@Controller('inventory')
@Auth()
export class InventoryController {
  constructor(private readonly inventoryService: InventoryService) {}

  @Post()
  @ApiOperation({ summary: 'สร้าง Inventory ใหม่' })
  @ApiResponse({ status: 201, description: 'สร้าง Inventory สำเร็จ', type: Inventory })
  @ApiResponse({ status: 400, description: 'ข้อมูลไม่ถูกต้อง' })
  create(@Body() createInventoryDto: CreateInventoryDto): Promise<Inventory> {
    return this.inventoryService.create(createInventoryDto);
  }

  @Get()
  @ApiOperation({ summary: 'ดึงรายการ Inventory ทั้งหมด' })
  @ApiResponse({ status: 200, description: 'ดึงข้อมูลสำเร็จ', type: [Inventory] })
  findAll(): Promise<Inventory[]> {
    return this.inventoryService.findAll();
  }

  @Get('branch/:branchId')
  @ApiOperation({ summary: 'ดึงรายการ Inventory ตามสาขา' })
  @ApiParam({ name: 'branchId', description: 'รหัสสาขา' })
  @ApiResponse({ status: 200, description: 'ดึงข้อมูลสำเร็จ', type: [Inventory] })
  findByBranch(@Param('branchId', ParseIntPipe) branchId: number): Promise<Inventory[]> {
    return this.inventoryService.findByBranch(branchId);
  }

  @Get('warehouse/:warehouseId')
  @ApiOperation({ summary: 'ดึงรายการ Inventory ตามคลังสินค้า' })
  @ApiParam({ name: 'warehouseId', description: 'รหัสคลังสินค้า' })
  @ApiResponse({ status: 200, description: 'ดึงข้อมูลสำเร็จ', type: [Inventory] })
  findByWarehouse(@Param('warehouseId', ParseIntPipe) warehouseId: number): Promise<Inventory[]> {
    return this.inventoryService.findByWarehouse(warehouseId);
  }

  @Get('product/:productId')
  @ApiOperation({ summary: 'ดึงรายการ Inventory ตามสินค้า' })
  @ApiParam({ name: 'productId', description: 'รหัสสินค้า' })
  @ApiResponse({ status: 200, description: 'ดึงข้อมูลสำเร็จ', type: [Inventory] })
  findByProduct(@Param('productId', ParseIntPipe) productId: number): Promise<Inventory[]> {
    return this.inventoryService.findByProduct(productId);
  }

  @Get('low-stock')
  @ApiOperation({ summary: 'ดึงรายการสินค้าที่ใกล้หมด' })
  @ApiQuery({ name: 'branchId', description: 'รหัสสาขา (ไม่บังคับ)', required: false })
  @ApiResponse({ status: 200, description: 'ดึงข้อมูลสำเร็จ', type: [Inventory] })
  getLowStockItems(@Query('branchId') branchId?: number): Promise<Inventory[]> {
    return this.inventoryService.getLowStockItems(branchId);
  }

  @Get('location/:productId/:branchId/:warehouseId')
  @ApiOperation({ summary: 'ดึง Inventory ตามสินค้าและที่ตั้ง' })
  @ApiParam({ name: 'productId', description: 'รหัสสินค้า' })
  @ApiParam({ name: 'branchId', description: 'รหัสสาขา' })
  @ApiParam({ name: 'warehouseId', description: 'รหัสคลังสินค้า' })
  @ApiResponse({ status: 200, description: 'ดึงข้อมูลสำเร็จ', type: Inventory })
  @ApiResponse({ status: 404, description: 'ไม่พบ Inventory' })
  findByProductAndLocation(
    @Param('productId', ParseIntPipe) productId: number,
    @Param('branchId', ParseIntPipe) branchId: number,
    @Param('warehouseId', ParseIntPipe) warehouseId: number,
  ): Promise<Inventory> {
    return this.inventoryService.findByProductAndLocation(productId, branchId, warehouseId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'ดึงข้อมูล Inventory ตาม ID' })
  @ApiParam({ name: 'id', description: 'รหัส Inventory' })
  @ApiResponse({ status: 200, description: 'ดึงข้อมูลสำเร็จ', type: Inventory })
  @ApiResponse({ status: 404, description: 'ไม่พบ Inventory' })
  findOne(@Param('id', ParseIntPipe) id: number): Promise<Inventory> {
    return this.inventoryService.findOne(id);
  }

  @Get(':id/transactions')
  @ApiOperation({ summary: 'ดึงประวัติการทำรายการ Inventory' })
  @ApiParam({ name: 'id', description: 'รหัส Inventory' })
  @ApiResponse({ status: 200, description: 'ดึงข้อมูลสำเร็จ', type: [InventoryTransaction] })
  getTransactionHistory(@Param('id', ParseIntPipe) id: number): Promise<InventoryTransaction[]> {
    return this.inventoryService.getTransactionHistory(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'แก้ไขข้อมูล Inventory' })
  @ApiParam({ name: 'id', description: 'รหัส Inventory' })
  @ApiResponse({ status: 200, description: 'แก้ไขข้อมูลสำเร็จ', type: Inventory })
  @ApiResponse({ status: 404, description: 'ไม่พบ Inventory' })
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateInventoryDto: UpdateInventoryDto,
  ): Promise<Inventory> {
    return this.inventoryService.update(id, updateInventoryDto);
  }

  @Post('adjustment')
  @ApiOperation({ summary: 'ปรับปรุงจำนวน Inventory' })
  @ApiResponse({ status: 201, description: 'ปรับปรุงสำเร็จ', type: InventoryTransaction })
  @ApiResponse({ status: 400, description: 'ข้อมูลไม่ถูกต้อง' })
  createAdjustment(
    @Body() adjustmentDto: InventoryAdjustmentDto,
    @Request() req: any,
  ): Promise<InventoryTransaction> {
    return this.inventoryService.createTransaction(adjustmentDto, req.user.id);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'ลบ Inventory' })
  @ApiParam({ name: 'id', description: 'รหัส Inventory' })
  @ApiResponse({ status: 200, description: 'ลบข้อมูลสำเร็จ' })
  @ApiResponse({ status: 404, description: 'ไม่พบ Inventory' })
  remove(@Param('id', ParseIntPipe) id: number): Promise<void> {
    return this.inventoryService.remove(id);
  }
}
