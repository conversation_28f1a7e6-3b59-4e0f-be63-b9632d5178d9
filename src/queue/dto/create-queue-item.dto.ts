import { ApiProperty } from "@nestjs/swagger";
import { IsN<PERSON>ber, IsString, IsArray, ValidateNested, IsOptional, IsBoolean, IsEnum } from "class-validator";
import { Type } from "class-transformer";
import { QueueStatus } from "../entities/queue.entity";

export class QueuePhotoDto {
    @ApiProperty({ example: 'qitem1_1.jpg' })
    @IsOptional()
    @IsString()
    readonly path?: string;

    @ApiProperty({ example: 1 })
    @IsOptional()
    @IsNumber()
    readonly step?: number;
}

export class QueueItemAllowanceDto {
    @ApiProperty({ example: 'น้ำ' })
    @IsOptional()
    @IsString()
    readonly name?: string;

    @ApiProperty({ example: 0.5 })
    @IsOptional()
    @IsNumber()
    readonly weight?: number;
}


export class CreateQueueItemDto {
    @ApiProperty({ example: 'เขียนโน๊ต' })
    @IsString()
    readonly note?: string;

    @ApiProperty({ example: 175 })
    @IsOptional()
    @IsNumber()
    readonly product?: number;

    @ApiProperty({ example: 'กระป๋องอลูมิเนียม' })
    @IsString()
    readonly productName?: string;

    @ApiProperty({ example: 1 })
    @IsOptional()
    @IsNumber()
    readonly unit?: number;

    @ApiProperty({ example: 'กิโลกรัม' })
    @IsString()
    readonly unitName?: string;

    @ApiProperty({ example: true, required: false })
    @IsOptional()
    @IsBoolean()
    readonly accepted?: boolean;

    @ApiProperty({ type: [QueuePhotoDto], required: false })
    @IsOptional()
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => QueuePhotoDto)
    readonly photos?: QueuePhotoDto[];

    @ApiProperty({ example: 30 })
    @IsOptional()
    @IsNumber()
    readonly grossWeight?: number;

    @ApiProperty({ example: 5 })
    @IsOptional()
    @IsNumber()
    readonly tareWeight?: number;

    @ApiProperty({ example: 0 })
    @IsOptional()
    @IsNumber()
    readonly deductedWeight?: number;

    @ApiProperty({ type: [QueueItemAllowanceDto], required: false })
    @IsOptional()
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => QueueItemAllowanceDto)
    readonly allowance?: QueueItemAllowanceDto[];

    @ApiProperty({ example: 24.5 })
    @IsOptional()
    @IsNumber()
    readonly netWeight?: number;

    @ApiProperty({ example: 10 })
    @IsOptional()
    @IsNumber()
    readonly price?: number;

    @ApiProperty({ example: 245 })
    @IsOptional()
    @IsNumber()
    readonly total?: number;


}

export class CreateQueueItemArrayDto {
    @ApiProperty({ type: [CreateQueueItemDto] })
    @IsOptional()
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => CreateQueueItemDto)
    readonly queueItems?: CreateQueueItemDto[];

    @ApiProperty({ type: [QueuePhotoDto], required: false })
    @IsOptional()
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => QueuePhotoDto)
    readonly queuePhotos?: QueuePhotoDto[];

    @ApiProperty({
        example: QueueStatus.WEIGHED,
        enum: QueueStatus
    })
    @IsOptional()
    @IsEnum(QueueStatus)
    readonly status?: QueueStatus;
}