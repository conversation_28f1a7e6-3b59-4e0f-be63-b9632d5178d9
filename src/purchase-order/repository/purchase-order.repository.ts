import { Injectable, Inject, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { DataSource } from 'typeorm';
import { Request } from 'express';
import { BaseRepository } from '../../common/repository/base-repository';
import { PurchaseOrder, PurchaseOrderStatus } from '../entities/purchase-order.entity';
import { CreatePurchaseOrderDto } from '../dto/create-purchase-order.dto';
import { UpdatePurchaseOrderDto } from '../dto/update-purchase-order.dto';

@Injectable({ scope: Scope.REQUEST })
export class PurchaseOrderRepository extends BaseRepository {
  constructor(dataSource: DataSource, @Inject(REQUEST) req: Request) {
    super(dataSource, req);
  }

  public get repository() {
    return this.getRepository(PurchaseOrder);
  }

  findAll() {
    return this.repository.find({
      relations: {
        items: {
          product: true
        },
        createdBy: true,
        approvedBy: true,
        branch: true
      },
      order: {
        createdAt: 'DESC'
      }
    });
  }

  findOne(id: number) {
    return this.repository.findOne({
      where: { id },
      relations: {
        items: {
          product: {
            category: true,
            unit: true
          }
        },
        createdBy: true,
        approvedBy: true,
        branch: true
      }
    });
  }

  findByPoNumber(poNumber: string) {
    return this.repository.findOne({
      where: { poNumber },
      relations: {
        items: {
          product: true
        },
        createdBy: true,
        approvedBy: true,
        branch: true
      }
    });
  }

  async create(data: CreatePurchaseOrderDto, userId: number) {
    const repository = this.repository;

    const purchaseOrder = repository.create({
      poNumber: await this.generatePoNumber(data.branchId),
      poDate: new Date(data.poDate),
      expectedDeliveryDate: data.expectedDeliveryDate ? new Date(data.expectedDeliveryDate) : null,
      status: data.status || PurchaseOrderStatus.DRAFT,
      supplierName: data.supplierName,
      supplierContact: data.supplierContact,
      supplierPhone: data.supplierPhone,
      supplierEmail: data.supplierEmail,
      supplierAddress: data.supplierAddress,
      subtotal: data.subtotal || 0,
      taxRate: data.taxRate || 0,
      taxAmount: data.taxAmount || 0,
      discountAmount: data.discountAmount || 0,
      totalAmount: data.totalAmount || 0,
      notes: data.notes,
      termsAndConditions: data.termsAndConditions,
      createdBy: {
        id: userId
      },
      branch: {
        id: data.branchId
      },
      items: data.items.map(item => ({
        productId: item.productId,
        productName: item.productName,
        productCode: item.productCode,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        totalPrice: item.totalPrice,
        receivedQuantity: 0,
        remainingQuantity: item.quantity,
        notes: item.notes,
        product: {
          id: item.productId
        }
      }))
    });

    return await repository.save(purchaseOrder);
  }

  async update(id: number, data: UpdatePurchaseOrderDto) {
    const repository = this.repository;
    
    const updateData: any = {
      ...data
    };

    if (data.poDate) {
      updateData.poDate = new Date(data.poDate);
    }
    if (data.expectedDeliveryDate) {
      updateData.expectedDeliveryDate = new Date(data.expectedDeliveryDate);
    }
    if (data.actualDeliveryDate) {
      updateData.actualDeliveryDate = new Date(data.actualDeliveryDate);
    }
    if (data.approvedAt) {
      updateData.approvedAt = new Date(data.approvedAt);
    }
    if (data.approvedBy) {
      updateData.approvedBy = { id: data.approvedBy };
    }

    await repository.update(id, updateData);
    return this.findOne(id);
  }

  async remove(id: number) {
    return await this.repository.softDelete(id);
  }

  private async generatePoNumber(branchId: number): Promise<string> {
    const currentDate = new Date();
    const year = currentDate.getFullYear().toString().slice(-2);
    const month = (currentDate.getMonth() + 1).toString().padStart(2, '0');
    
    // หาเลขที่ PO ล่าสุดในเดือนนี้
    const prefix = `PO${year}${month}`;
    const lastPo = await this.repository
      .createQueryBuilder('po')
      .where('po.poNumber LIKE :prefix', { prefix: `${prefix}%` })
      .orderBy('po.poNumber', 'DESC')
      .getOne();

    let sequence = 1;
    if (lastPo) {
      const lastSequence = parseInt(lastPo.poNumber.slice(-4));
      sequence = lastSequence + 1;
    }

    return `${prefix}${sequence.toString().padStart(4, '0')}`;
  }
}
