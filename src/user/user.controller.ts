import { Controller, Get, Post, Body, Put, Param, Delete, UseInterceptors, ClassSerializerInterceptor, ParseIntPipe, HttpCode, HttpStatus, Req, Query } from '@nestjs/common';
import { USER_PAGINATION_CONFIG, UserService } from './user.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';
import { ApiConsumes, ApiTags } from '@nestjs/swagger';
import { Auth } from 'src/auth/decorators/auth.decorator';
import { Request } from 'express';
import { DateTime } from 'luxon';
import { ForceChangePasswordDto } from './dto/force-change-password.dto';

@Controller('user')
@ApiTags('ผู้ใช้งาน')
@Auth()
@UseInterceptors(ClassSerializerInterceptor)
export class UserController {
  constructor(private readonly userService: UserService) { }


  @Get('/:id/orders')
  orders(@Param('id', ParseIntPipe) id: number, @Query('start') start: string, @Query('end') end: string, @Query('branch') branch: number) {
    const startDate = DateTime.fromSQL(start).startOf('day').toJSDate();
    const endDate = DateTime.fromSQL(end).endOf('day').toJSDate();

    return this.userService.orders(id, startDate, endDate, branch);
  }

  @Put('/:id/force-change-password')
  forceChangePassword(@Param('id', ParseIntPipe) id: number,@Body() payload: ForceChangePasswordDto) {
    return this.userService.forceChangePassword(id, payload.password);
  }

  @Get('profile')
  async profile(@Req() req: Request) {
    const userId = req.user['sub'];

    return this.userService.findById(userId);
  }

  @Get('datatables')
  @HttpCode(HttpStatus.OK)
  @ApiPaginationQuery(USER_PAGINATION_CONFIG)
  datatables(@Paginate() query: PaginateQuery) {
    return this.userService.datatables(query);
  }

  @Post()
  @ApiConsumes('application/x-www-form-urlencoded')
  @ApiConsumes('application/json')
  create(@Body() createUserDto: CreateUserDto) {
    return this.userService.create(createUserDto);
  }

  @Get()
  findAll() {
    return this.userService.findAll();
  }

  @Get(':id')
  findOne(@Param('id', ParseIntPipe) id: string) {
    return this.userService.findOne(+id);
  }

  @Put(':id')
  @ApiConsumes('application/json')
  @ApiConsumes('application/x-www-form-urlencoded')
  update(@Param('id', ParseIntPipe) id: string, @Body() updateUserDto: UpdateUserDto) {
    return this.userService.update(+id, updateUserDto);
  }

  @Delete(':id')
  remove(@Param('id', ParseIntPipe) id: string) {
    return this.userService.remove(+id);
  }
}
