import { MiddlewareConsumer, Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserModule } from './user/user.module';
import { RoleModule } from './role/role.module';
import { PermissionModule } from './permission/permission.module';
import { AuthModule } from './auth/auth.module';
import { StoreModule } from './store/store.module';
import { BranchModule } from './branch/branch.module';
import { CategoryModule } from './category/category.module';
import { ProductModule } from './product/product.module';
import { CustomerModule } from './customer/customer.module';
import { OrderModule } from './order/order.module';
import { PaymentMethodModule } from './payment-method/payment-method.module';
import { UploadModule } from './upload/upload.module';
import typeorm from './config/typeorm';
import { ServeStaticModule } from '@nestjs/serve-static';
import { join } from 'path';
import * as path from 'path';
import { ShiftModule } from './shift/shift.module';
import { UnitModule } from './unit/unit.module';
import { PaysolutionModule } from './paysolution/paysolution.module';
import { PaymentModule } from './payment/payment.module';
import { PromotionModule } from './promotion/promotion.module';
import { ReportModule } from './report/report.module';
import { LevelModule } from './level/level.module';
import { NamePrefixModule } from './name-prefix/name-prefix.module';
import { PanelModule } from './panel/panel.module';
import { InvoiceModule } from './invoice/invoice.module';
import { RewardModule } from './reward/reward.module';
import { AppLoggerMiddleware } from './app-logger.middleware';
import { QueueModule } from './queue/queue.module';
import { AcceptLanguageResolver, I18nModule, QueryResolver } from 'nestjs-i18n';
import { PdfModule } from './pdf/pdf.module';
import { AllowanceModule } from './allowance/allowance.module';
import { DashboardModule } from './dashboard/dashboard.module';
import { PurchaseOrderModule } from './purchase-order/purchase-order.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [typeorm]
    }),
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '..', 'uploads'),
    }),
    TypeOrmModule.forRootAsync({
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => (configService.get('typeorm'))
    }),
    I18nModule.forRoot({
      fallbackLanguage: 'en',
      loaderOptions: {
        path: path.join(__dirname, '/i18n/'),
        watch: true,
      },
      resolvers: [
        { use: QueryResolver, options: ['lang'] },
        AcceptLanguageResolver,
      ],
      typesOutputPath: path.join(__dirname, '../src/generated/i18n.generated.ts'),
    }),
    AuthModule,
    UserModule,
    RoleModule,
    PermissionModule,
    StoreModule,
    BranchModule,
    CategoryModule,
    ProductModule,
    CustomerModule,
    OrderModule,
    PaymentMethodModule,
    UploadModule,
    ShiftModule,
    LevelModule,
    UnitModule,
    PaysolutionModule,
    PaymentModule,
    PromotionModule,
    ReportModule,
    NamePrefixModule,
    PanelModule,
    InvoiceModule,
    RewardModule,
    QueueModule,
    PdfModule,
    AllowanceModule,
    DashboardModule,
    PurchaseOrderModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {
  configure(consumer: MiddlewareConsumer): void {
    consumer.apply(AppLoggerMiddleware).forRoutes('/**');
  }
}
