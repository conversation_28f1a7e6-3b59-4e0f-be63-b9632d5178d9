import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateStoreDto } from './dto/create-store.dto';
import { UpdateStoreDto } from './dto/update-store.dto';
import { Store } from './entities/store.entity';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { PaginateConfig, PaginateQuery, Paginated, paginate } from 'nestjs-paginate';

export const STORE_PAGINATION_CONFIG: PaginateConfig<Store> = {
  sortableColumns: ['id', 'name'],
  select: ['id', 'code', 'name', 'createdAt'],
};
@Injectable()
export class StoreService {
  constructor(
    @InjectRepository(Store)
    private storeRepository: Repository<Store>,
  ) { }

  create(createStoreDto: CreateStoreDto) {
    const store = this.storeRepository.create(createStoreDto);

    return this.storeRepository.save(store);
  }

  findAll() {
    return this.storeRepository.find();
  }

  async findOne(id: number) {
    const store = await this.storeRepository.findOne({ where: { id } });

    if (!store) throw new NotFoundException('store not found');

    return store;
  }

  async update(id: number, updateStoreDto: UpdateStoreDto) {
    const store = await this.findOneById(id);
    if (!store) throw new NotFoundException('store not found');

    return this.storeRepository.update(id, updateStoreDto);
  }

  async remove(id: number) {
    const store = await this.findOneById(id);
    if (!store) throw new NotFoundException('store not found');

    await this.storeRepository.softDelete(id);
  }

  findOneById(id: number) {
    return this.storeRepository.findOneBy({ id });
  }

  async datatables(query: PaginateQuery): Promise<Paginated<Store>> {
    return paginate(query, this.storeRepository, STORE_PAGINATION_CONFIG);
  }
}
