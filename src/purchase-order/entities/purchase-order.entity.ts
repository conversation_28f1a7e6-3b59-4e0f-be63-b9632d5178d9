import { Column, Entity, Index, Join<PERSON><PERSON>umn, ManyToOne, OneToMany } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { DecimalColumnTransformer } from "../../common/decimal-column-transformer";
import { User } from "../../user/entities/user.entity";
import { Branch } from "../../branch/entities/branch.entity";
import { PurchaseOrderItem } from "./purchase-order-item.entity";

export enum PurchaseOrderStatus {
    DRAFT = "draft",
    PENDING = "pending",
    APPROVED = "approved",
    ORDERED = "ordered",
    RECEIVED = "received",
    COMPLETED = "completed",
    CANCELLED = "cancelled",
}

@Entity()
export class PurchaseOrder extends CustomBaseEntity {
    @Index()
    @Column({ name: 'po_number', unique: true })
    poNumber: string;

    @Column({ name: 'po_date' })
    poDate: Date;

    @Column({ name: 'expected_delivery_date', nullable: true })
    expectedDeliveryDate: Date;

    @Column({ name: 'actual_delivery_date', nullable: true })
    actualDeliveryDate: Date;

    @Column({ name: 'status', type: 'enum', enum: PurchaseOrderStatus, default: PurchaseOrderStatus.DRAFT })
    status: PurchaseOrderStatus;

    @Column({ name: 'supplier_name' })
    supplierName: string;

    @Column({ name: 'supplier_contact', nullable: true })
    supplierContact: string;

    @Column({ name: 'supplier_phone', nullable: true })
    supplierPhone: string;

    @Column({ name: 'supplier_email', nullable: true })
    supplierEmail: string;

    @Column({ name: 'supplier_address', type: 'text', nullable: true })
    supplierAddress: string;

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), default: 0 })
    subtotal: number;

    @Column({ name: 'tax_rate', type: 'numeric', transformer: new DecimalColumnTransformer(), default: 0 })
    taxRate: number;

    @Column({ name: 'tax_amount', type: 'numeric', transformer: new DecimalColumnTransformer(), default: 0 })
    taxAmount: number;

    @Column({ name: 'discount_amount', type: 'numeric', transformer: new DecimalColumnTransformer(), default: 0 })
    discountAmount: number;

    @Column({ name: 'total_amount', type: 'numeric', transformer: new DecimalColumnTransformer(), default: 0 })
    totalAmount: number;

    @Column({ type: 'text', nullable: true })
    notes: string;

    @Column({ name: 'terms_and_conditions', type: 'text', nullable: true })
    termsAndConditions: string;

    @ManyToOne(() => User, (user) => user.purchaseOrders)
    @JoinColumn({ name: 'created_by' })
    createdBy: User;

    @ManyToOne(() => User, { nullable: true })
    @JoinColumn({ name: 'approved_by' })
    approvedBy: User;

    @Column({ name: 'approved_at', nullable: true })
    approvedAt: Date;

    @ManyToOne(() => Branch, (branch) => branch.purchaseOrders)
    @JoinColumn({ name: 'branch_id' })
    branch: Branch;

    @OneToMany(() => PurchaseOrderItem, (item) => item.purchaseOrder, { cascade: true })
    items: PurchaseOrderItem[];

    constructor(partial?: Partial<PurchaseOrder>) {
        super();
        if (partial) {
            Object.assign(this, partial);
        }
    }
}
