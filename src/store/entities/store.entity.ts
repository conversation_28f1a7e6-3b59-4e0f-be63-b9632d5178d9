import { Column, Entity, Index, OneToMany, Unique } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { Branch } from "../../branch/entities/branch.entity";

@Entity()
@Unique(['code'])
export class Store extends CustomBaseEntity {
    @Column()
    @Index({ unique: true })
    code: string;

    @Column()
    name: string;

    @Column({ nullable: true })
    address: string;

    @OneToMany(() => Branch, (_) => _.store)
    branchs: Array<Branch>;

    constructor(partial?: Partial<Store>) {
        super();
        if (partial) {
            Object.assign(this, partial)
        }
    }
}
