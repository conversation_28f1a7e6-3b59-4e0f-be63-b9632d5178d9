import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { NotFoundException, BadRequestException } from '@nestjs/common';
import { PurchaseOrderService } from './purchase-order.service';
import { PurchaseOrder, PurchaseOrderStatus } from './entities/purchase-order.entity';
import { PurchaseOrderItem } from './entities/purchase-order-item.entity';
import { PurchaseOrderRepository } from './repository/purchase-order.repository';
import { CreatePurchaseOrderDto } from './dto/create-purchase-order.dto';

describe('PurchaseOrderService', () => {
  let service: PurchaseOrderService;
  let purchaseOrderRepository: jest.Mocked<Repository<PurchaseOrder>>;
  let purchaseOrderItemRepository: jest.Mocked<Repository<PurchaseOrderItem>>;
  let purchaseOrderRepo: jest.Mocked<PurchaseOrderRepository>;

  const mockPurchaseOrder = {
    id: 1,
    poNumber: 'PO240001',
    poDate: new Date('2024-01-01'),
    status: PurchaseOrderStatus.DRAFT,
    supplierName: 'Test Supplier',
    totalAmount: 1000,
    items: [
      {
        id: 1,
        productId: 1,
        productName: 'Test Product',
        quantity: 10,
        unitPrice: 100,
        totalPrice: 1000,
        receivedQuantity: 0,
        remainingQuantity: 10
      }
    ]
  };

  const mockCreateDto: CreatePurchaseOrderDto = {
    poDate: '2024-01-01',
    supplierName: 'Test Supplier',
    branchId: 1,
    items: [
      {
        productId: 1,
        productName: 'Test Product',
        productCode: 'TP001',
        quantity: 10,
        unitPrice: 100,
        totalPrice: 1000
      }
    ]
  };

  beforeEach(async () => {
    const mockRepository = {
      find: jest.fn(),
      findOne: jest.fn(),
      create: jest.fn(),
      save: jest.fn(),
      update: jest.fn(),
      softDelete: jest.fn(),
    };

    const mockPurchaseOrderRepo = {
      findAll: jest.fn(),
      findOne: jest.fn(),
      findByPoNumber: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      remove: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PurchaseOrderService,
        {
          provide: getRepositoryToken(PurchaseOrder),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(PurchaseOrderItem),
          useValue: mockRepository,
        },
        {
          provide: PurchaseOrderRepository,
          useValue: mockPurchaseOrderRepo,
        },
      ],
    }).compile();

    service = module.get<PurchaseOrderService>(PurchaseOrderService);
    purchaseOrderRepository = module.get(getRepositoryToken(PurchaseOrder));
    purchaseOrderItemRepository = module.get(getRepositoryToken(PurchaseOrderItem));
    purchaseOrderRepo = module.get(PurchaseOrderRepository);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findAll', () => {
    it('should return an array of purchase orders', async () => {
      purchaseOrderRepo.findAll.mockResolvedValue([mockPurchaseOrder]);

      const result = await service.findAll();

      expect(result).toEqual([mockPurchaseOrder]);
      expect(purchaseOrderRepo.findAll).toHaveBeenCalled();
    });
  });

  describe('findOne', () => {
    it('should return a purchase order', async () => {
      purchaseOrderRepo.findOne.mockResolvedValue(mockPurchaseOrder);

      const result = await service.findOne(1);

      expect(result).toEqual(mockPurchaseOrder);
      expect(purchaseOrderRepo.findOne).toHaveBeenCalledWith(1);
    });

    it('should throw NotFoundException when purchase order not found', async () => {
      purchaseOrderRepo.findOne.mockResolvedValue(null);

      await expect(service.findOne(999)).rejects.toThrow(NotFoundException);
    });
  });

  describe('create', () => {
    it('should create a new purchase order', async () => {
      purchaseOrderRepo.create.mockResolvedValue(mockPurchaseOrder);

      const result = await service.create(mockCreateDto, 1);

      expect(result).toEqual(mockPurchaseOrder);
      expect(purchaseOrderRepo.create).toHaveBeenCalledWith(
        expect.objectContaining({
          ...mockCreateDto,
          subtotal: 1000,
          taxAmount: 0,
          totalAmount: 1000
        }),
        1
      );
    });
  });

  describe('update', () => {
    it('should update a purchase order', async () => {
      const updatedPo = { ...mockPurchaseOrder, supplierName: 'Updated Supplier' };
      purchaseOrderRepo.findOne.mockResolvedValue(mockPurchaseOrder);
      purchaseOrderRepo.update.mockResolvedValue(updatedPo);

      const result = await service.update(1, { supplierName: 'Updated Supplier' });

      expect(result).toEqual(updatedPo);
      expect(purchaseOrderRepo.update).toHaveBeenCalledWith(1, { supplierName: 'Updated Supplier' });
    });

    it('should throw BadRequestException when trying to update completed PO', async () => {
      const completedPo = { ...mockPurchaseOrder, status: PurchaseOrderStatus.COMPLETED };
      purchaseOrderRepo.findOne.mockResolvedValue(completedPo);

      await expect(service.update(1, { supplierName: 'Updated' })).rejects.toThrow(BadRequestException);
    });
  });

  describe('remove', () => {
    it('should remove a draft purchase order', async () => {
      purchaseOrderRepo.findOne.mockResolvedValue(mockPurchaseOrder);
      purchaseOrderRepo.remove.mockResolvedValue({ affected: 1 });

      const result = await service.remove(1);

      expect(result).toEqual({ affected: 1 });
      expect(purchaseOrderRepo.remove).toHaveBeenCalledWith(1);
    });

    it('should throw BadRequestException when trying to delete non-draft PO', async () => {
      const approvedPo = { ...mockPurchaseOrder, status: PurchaseOrderStatus.APPROVED };
      purchaseOrderRepo.findOne.mockResolvedValue(approvedPo);

      await expect(service.remove(1)).rejects.toThrow(BadRequestException);
    });
  });

  describe('approve', () => {
    it('should approve a pending purchase order', async () => {
      const pendingPo = { ...mockPurchaseOrder, status: PurchaseOrderStatus.PENDING };
      const approvedPo = { ...pendingPo, status: PurchaseOrderStatus.APPROVED };
      
      purchaseOrderRepo.findOne.mockResolvedValue(pendingPo);
      purchaseOrderRepo.update.mockResolvedValue(approvedPo);

      const result = await service.approve(1, 2);

      expect(result).toEqual(approvedPo);
      expect(purchaseOrderRepo.update).toHaveBeenCalledWith(1, {
        status: PurchaseOrderStatus.APPROVED,
        approvedBy: 2,
        approvedAt: expect.any(String)
      });
    });

    it('should throw BadRequestException when trying to approve non-pending PO', async () => {
      purchaseOrderRepo.findOne.mockResolvedValue(mockPurchaseOrder);

      await expect(service.approve(1, 2)).rejects.toThrow(BadRequestException);
    });
  });

  describe('changeStatus', () => {
    it('should change status when transition is valid', async () => {
      const updatedPo = { ...mockPurchaseOrder, status: PurchaseOrderStatus.PENDING };
      purchaseOrderRepo.findOne.mockResolvedValue(mockPurchaseOrder);
      purchaseOrderRepo.update.mockResolvedValue(updatedPo);

      const result = await service.changeStatus(1, PurchaseOrderStatus.PENDING);

      expect(result).toEqual(updatedPo);
      expect(purchaseOrderRepo.update).toHaveBeenCalledWith(1, { status: PurchaseOrderStatus.PENDING });
    });

    it('should throw BadRequestException when transition is invalid', async () => {
      purchaseOrderRepo.findOne.mockResolvedValue(mockPurchaseOrder);

      await expect(service.changeStatus(1, PurchaseOrderStatus.COMPLETED)).rejects.toThrow(BadRequestException);
    });
  });
});
