import { Column, Entity, ManyToOne } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { Queue } from "./queue.entity";
import { Expose } from "class-transformer";
import { QueueItem } from "./queue-item.entity";

@Entity()
export class QueueItemPhoto extends CustomBaseEntity {
    @Column({ nullable: true })
    step: number;

    @Column()
    path: string;

    @ManyToOne(() => QueueItem, (queue) => queue.photos, { onDelete: 'CASCADE' })
    queueItem: QueueItem;

    @Expose()
    get pathUrl(): string {
        if (!this.path) return null;

        // ถ้า path ขึ้นต้นด้วย http:// หรือ https:// ให้ return path เลย
        if (this.path.startsWith('http://') || this.path.startsWith('https://')) {
            return this.path;
        }

        // ถ้าไม่ใช่ URL สมบูรณ์ ให้ต่อ base URL เข้าไป
        return `${process.env.APP_URL}/${this.path}`;
    }
}