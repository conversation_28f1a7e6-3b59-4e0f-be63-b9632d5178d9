import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsArray, IsDateString, IsEmail, IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString, Min, ValidateNested } from "class-validator";
import { PurchaseOrderStatus } from "../entities/purchase-order.entity";
import { CreatePurchaseOrderItemDto } from "./create-purchase-order-item.dto";

export class CreatePurchaseOrderDto {
    @ApiProperty({ description: 'วันที่สั่งซื้อ' })
    @IsNotEmpty()
    @IsDateString()
    poDate: string;

    @ApiProperty({ description: 'วันที่คาดว่าจะได้รับสินค้า', required: false })
    @IsOptional()
    @IsDateString()
    expectedDeliveryDate?: string;

    @ApiProperty({ 
        description: 'สถานะใบสั่งซื้อ', 
        enum: PurchaseOrderStatus,
        default: PurchaseOrderStatus.DRAFT 
    })
    @IsOptional()
    @IsEnum(PurchaseOrderStatus)
    status?: PurchaseOrderStatus;

    @ApiProperty({ description: 'ชื่อผู้จำหน่าย' })
    @IsNotEmpty()
    @IsString()
    supplierName: string;

    @ApiProperty({ description: 'ชื่อผู้ติดต่อ', required: false })
    @IsOptional()
    @IsString()
    supplierContact?: string;

    @ApiProperty({ description: 'เบอร์โทรศัพท์ผู้จำหน่าย', required: false })
    @IsOptional()
    @IsString()
    supplierPhone?: string;

    @ApiProperty({ description: 'อีเมลผู้จำหน่าย', required: false })
    @IsOptional()
    @IsEmail()
    supplierEmail?: string;

    @ApiProperty({ description: 'ที่อยู่ผู้จำหน่าย', required: false })
    @IsOptional()
    @IsString()
    supplierAddress?: string;

    @ApiProperty({ description: 'ยอดรวมก่อนภาษี', default: 0 })
    @IsOptional()
    @IsNumber()
    @Min(0)
    subtotal?: number;

    @ApiProperty({ description: 'อัตราภาษี (%)', default: 0 })
    @IsOptional()
    @IsNumber()
    @Min(0)
    taxRate?: number;

    @ApiProperty({ description: 'จำนวนเงินภาษี', default: 0 })
    @IsOptional()
    @IsNumber()
    @Min(0)
    taxAmount?: number;

    @ApiProperty({ description: 'ส่วนลด', default: 0 })
    @IsOptional()
    @IsNumber()
    @Min(0)
    discountAmount?: number;

    @ApiProperty({ description: 'ยอดรวมสุทธิ', default: 0 })
    @IsOptional()
    @IsNumber()
    @Min(0)
    totalAmount?: number;

    @ApiProperty({ description: 'หมายเหตุ', required: false })
    @IsOptional()
    @IsString()
    notes?: string;

    @ApiProperty({ description: 'เงื่อนไขและข้อตกลง', required: false })
    @IsOptional()
    @IsString()
    termsAndConditions?: string;

    @ApiProperty({ description: 'รหัสสาขา' })
    @IsNotEmpty()
    @IsNumber()
    branchId: number;

    @ApiProperty({ 
        description: 'รายการสินค้า',
        type: [CreatePurchaseOrderItemDto]
    })
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => CreatePurchaseOrderItemDto)
    items: CreatePurchaseOrderItemDto[];
}
