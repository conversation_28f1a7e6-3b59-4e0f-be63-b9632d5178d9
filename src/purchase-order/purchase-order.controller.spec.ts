import { Test, TestingModule } from '@nestjs/testing';
import { PurchaseOrderController } from './purchase-order.controller';
import { PurchaseOrderService } from './purchase-order.service';
import { PurchaseOrderStatus } from './entities/purchase-order.entity';
import { CreatePurchaseOrderDto } from './dto/create-purchase-order.dto';
import { UpdatePurchaseOrderDto } from './dto/update-purchase-order.dto';

describe('PurchaseOrderController', () => {
  let controller: PurchaseOrderController;
  let service: jest.Mocked<PurchaseOrderService>;

  const mockPurchaseOrder = {
    id: 1,
    poNumber: 'PO240001',
    poDate: new Date('2024-01-01'),
    status: PurchaseOrderStatus.DRAFT,
    supplierName: 'Test Supplier',
    totalAmount: 1000,
    items: []
  };

  const mockCreateDto: CreatePurchaseOrderDto = {
    poDate: '2024-01-01',
    supplierName: 'Test Supplier',
    branchId: 1,
    items: [
      {
        productId: 1,
        productName: 'Test Product',
        productCode: 'TP001',
        quantity: 10,
        unitPrice: 100,
        totalPrice: 1000
      }
    ]
  };

  const mockRequest = {
    user: { sub: 1 }
  } as any;

  beforeEach(async () => {
    const mockService = {
      datatables: jest.fn(),
      findAll: jest.fn(),
      findOne: jest.fn(),
      findByPoNumber: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      remove: jest.fn(),
      approve: jest.fn(),
      changeStatus: jest.fn(),
      receiveItems: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [PurchaseOrderController],
      providers: [
        {
          provide: PurchaseOrderService,
          useValue: mockService,
        },
      ],
    }).compile();

    controller = module.get<PurchaseOrderController>(PurchaseOrderController);
    service = module.get(PurchaseOrderService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('datatables', () => {
    it('should return paginated purchase orders', async () => {
      const mockQuery = {} as any;
      const mockResult = { data: [mockPurchaseOrder], meta: {} };
      service.datatables.mockResolvedValue(mockResult);

      const result = await controller.datatables(mockQuery);

      expect(result).toEqual(mockResult);
      expect(service.datatables).toHaveBeenCalledWith(mockQuery);
    });
  });

  describe('create', () => {
    it('should create a new purchase order', async () => {
      service.create.mockResolvedValue(mockPurchaseOrder);

      const result = await controller.create(mockRequest, mockCreateDto);

      expect(result).toEqual(mockPurchaseOrder);
      expect(service.create).toHaveBeenCalledWith(mockCreateDto, 1);
    });
  });

  describe('findAll', () => {
    it('should return all purchase orders', async () => {
      service.findAll.mockResolvedValue([mockPurchaseOrder]);

      const result = await controller.findAll();

      expect(result).toEqual([mockPurchaseOrder]);
      expect(service.findAll).toHaveBeenCalled();
    });
  });

  describe('findOne', () => {
    it('should return a purchase order by id', async () => {
      service.findOne.mockResolvedValue(mockPurchaseOrder);

      const result = await controller.findOne(1);

      expect(result).toEqual(mockPurchaseOrder);
      expect(service.findOne).toHaveBeenCalledWith(1);
    });
  });

  describe('findByPoNumber', () => {
    it('should return a purchase order by PO number', async () => {
      service.findByPoNumber.mockResolvedValue(mockPurchaseOrder);

      const result = await controller.findByPoNumber('PO240001');

      expect(result).toEqual(mockPurchaseOrder);
      expect(service.findByPoNumber).toHaveBeenCalledWith('PO240001');
    });
  });

  describe('update', () => {
    it('should update a purchase order', async () => {
      const updateDto: UpdatePurchaseOrderDto = { supplierName: 'Updated Supplier' };
      const updatedPo = { ...mockPurchaseOrder, supplierName: 'Updated Supplier' };
      service.update.mockResolvedValue(updatedPo);

      const result = await controller.update(1, updateDto);

      expect(result).toEqual(updatedPo);
      expect(service.update).toHaveBeenCalledWith(1, updateDto);
    });
  });

  describe('remove', () => {
    it('should remove a purchase order', async () => {
      service.remove.mockResolvedValue({ affected: 1 });

      const result = await controller.remove(1);

      expect(result).toEqual({ affected: 1 });
      expect(service.remove).toHaveBeenCalledWith(1);
    });
  });

  describe('approve', () => {
    it('should approve a purchase order', async () => {
      const approvedPo = { ...mockPurchaseOrder, status: PurchaseOrderStatus.APPROVED };
      service.approve.mockResolvedValue(approvedPo);

      const result = await controller.approve(1, mockRequest);

      expect(result).toEqual(approvedPo);
      expect(service.approve).toHaveBeenCalledWith(1, 1);
    });
  });

  describe('changeStatus', () => {
    it('should change purchase order status', async () => {
      const updatedPo = { ...mockPurchaseOrder, status: PurchaseOrderStatus.PENDING };
      service.changeStatus.mockResolvedValue(updatedPo);

      const result = await controller.changeStatus(1, PurchaseOrderStatus.PENDING);

      expect(result).toEqual(updatedPo);
      expect(service.changeStatus).toHaveBeenCalledWith(1, PurchaseOrderStatus.PENDING);
    });
  });

  describe('receiveItems', () => {
    it('should receive items for a purchase order', async () => {
      const receivedItems = [{ itemId: 1, receivedQuantity: 5 }];
      const updatedPo = { ...mockPurchaseOrder, status: PurchaseOrderStatus.RECEIVED };
      service.receiveItems.mockResolvedValue(updatedPo);

      const result = await controller.receiveItems(1, receivedItems);

      expect(result).toEqual(updatedPo);
      expect(service.receiveItems).toHaveBeenCalledWith(1, receivedItems);
    });
  });
});
