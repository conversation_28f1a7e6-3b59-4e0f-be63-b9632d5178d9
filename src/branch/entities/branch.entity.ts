import { CustomBaseEntity } from "../../common/entities";
import { Store } from "../../store/entities/store.entity";
import { Column, Entity, Index, JoinColumn, ManyToOne, OneToMany, Unique } from "typeorm";
import { Shift } from "../../shift/entities/shift.entity";
import { Order } from "../../order/entities/order.entity";
import { Product } from "../../product/entities/product.entity";
import { Queue } from "src/queue/entities/queue.entity";
import { PurchaseOrder } from "../../purchase-order/entities/purchase-order.entity";
import { Warehouse } from "../../warehouse/entities/warehouse.entity";
import { Inventory } from "../../inventory/entities/inventory.entity";

@Entity()
@Unique(['code'])
export class Branch extends CustomBaseEntity {
    @Column()
    @Index({ unique: true })
    code: string;

    @Column()
    name: string;

    @Column({ nullable: true })
    address: string;
    
    @ManyToOne(() => Store, (_) => _.branchs)
    @JoinColumn({ name: 'store_id' })
    store: Store;

    @OneToMany(() => Shift,(_)=>_.branch)
    shifts: Shift[]

    @OneToMany(() => Order,(_)=>_.branch)
    orders: Order[]

    @OneToMany(() => Product, (_) => _.branch)
    products: Array<Product>;

    @OneToMany(() => PurchaseOrder, (_) => _.branch)
    purchaseOrders: Array<PurchaseOrder>;

    @OneToMany(() => Warehouse, (_) => _.branch)
    warehouses: Array<Warehouse>;

    @OneToMany(() => Inventory, (_) => _.branch)
    inventories: Array<Inventory>;

    constructor(partial?: Partial<Store>) {
        super();
        if (partial) {
            Object.assign(this, partial)
        }
    }

    @OneToMany(() => Queue, (_) => _.branch)
    queres: Array<Queue>;
}
