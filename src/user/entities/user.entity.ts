import { Exclude, Expose } from "class-transformer";
import { CustomBaseEntity } from "../../common/entities/custom-base.entity";
import { Role } from "../../role/entities/role.entity";
import { Column, Entity, Index, JoinColumn, JoinTable, ManyToMany, ManyToOne, OneToMany, Unique } from "typeorm";
import { Shift } from "../../shift/entities/shift.entity";
import { Order } from "../../order/entities/order.entity";
import { Invoice } from "src/invoice/entities/invoice.entity";
import { Queue } from "src/queue/entities/queue.entity";
import { PurchaseOrder } from "../../purchase-order/entities/purchase-order.entity";

@Entity()
@Unique(['username', 'code'])
export class User extends CustomBaseEntity {
    @Column()
    @Index({ unique: true })
    username: string;

    @Column()
    @Exclude()
    password: string;

    @Column()
    @Index({ unique: true })
    code: string;

    @Column({ name: 'first_name' })
    firstName: string;

    @Column({ name: 'last_name' })
    lastName: string;

    @Column({ name: 'phone_number', nullable: true, length: 10 })
    phoneNumber: string;

    @Column({ name: 'is_active', default: true })
    isActive: boolean;

    @ManyToOne(() => Role, (_) => _.users)
    role: Role;

    @Exclude()
    @Column({ name: 'refresh_token', nullable: true })
    refreshToken: string;

    @OneToMany(() => Shift, (_) => _.user)
    shifts: Shift[];

    @OneToMany(() => Order, (_) => _.user)
    orders: Order[];

    @OneToMany(() => Invoice, (_) => _.user)
    invoices: Invoice[];

    @OneToMany(() => Queue, (_) => _.createdBy)
    queues: Queue[];

    @OneToMany(() => Queue, (_) => _.servedBy)
    servedQueues: Queue[];

    @OneToMany(() => Queue, (_) => _.officer)
    officerQueues: Queue[];

    @OneToMany(() => PurchaseOrder, (_) => _.createdBy)
    purchaseOrders: PurchaseOrder[];

    @Expose()
    get fullName(): string {
        return `${this.firstName} ${this.lastName}`;
    }
}
