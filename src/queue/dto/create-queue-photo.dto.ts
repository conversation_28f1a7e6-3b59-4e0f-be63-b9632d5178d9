import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsEnum, IsOptional } from "class-validator";
import { QueuePhotoStatus } from "../entities/queue-photo.entity";

export class CreateQueuePhotoDto {
    @ApiProperty({ example: 'q1_1.jpg' })
    @IsOptional()
    readonly path?: string;

    @ApiProperty({
        example: QueuePhotoStatus.INCOME,
        enum: QueuePhotoStatus,
    })
    @IsOptional()
    @IsEnum(QueuePhotoStatus)
    readonly status: QueuePhotoStatus;
}
