import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { DecimalColumnTransformer } from "../../common/decimal-column-transformer";
import { Product } from "../../product/entities/product.entity";
import { PurchaseOrder } from "./purchase-order.entity";

@Entity()
export class PurchaseOrderItem extends CustomBaseEntity {
    @ManyToOne(() => PurchaseOrder, (po) => po.items, { onDelete: 'CASCADE' })
    @JoinColumn({ name: 'purchase_order_id' })
    purchaseOrder: PurchaseOrder;

    @ManyToOne(() => Product, (product) => product.purchaseOrderItems)
    @JoinColumn({ name: 'product_id' })
    product: Product;

    @Column({ name: 'product_name' })
    productName: string;

    @Column({ name: 'product_code' })
    productCode: string;

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer() })
    quantity: number;

    @Column({ name: 'unit_price', type: 'numeric', transformer: new DecimalColumnTransformer() })
    unitPrice: number;

    @Column({ name: 'total_price', type: 'numeric', transformer: new DecimalColumnTransformer() })
    totalPrice: number;

    @Column({ name: 'received_quantity', type: 'numeric', transformer: new DecimalColumnTransformer(), default: 0 })
    receivedQuantity: number;

    @Column({ name: 'remaining_quantity', type: 'numeric', transformer: new DecimalColumnTransformer(), default: 0 })
    remainingQuantity: number;

    @Column({ type: 'text', nullable: true })
    notes: string;

    constructor(partial?: Partial<PurchaseOrderItem>) {
        super();
        if (partial) {
            Object.assign(this, partial);
        }
    }
}
