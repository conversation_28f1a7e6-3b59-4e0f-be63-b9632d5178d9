import { Column, Entity, ManyToOne } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { Queue } from "./queue.entity";
import { Expose } from "class-transformer";

export enum QueuePhotoStatus {
    INCOME = 'income',
    WEIGHT = 'weight',
    SORT = 'sort',
    DONE = 'done',
}


@Entity()
export class QueuePhoto extends CustomBaseEntity {
    @Column()
    path: string;

    @Column({ nullable: true })
    type: string;

    @Column({ type: 'enum', enum: QueuePhotoStatus, default: QueuePhotoStatus.INCOME })
    status: QueuePhotoStatus;

    @ManyToOne(() => Queue, (queue) => queue.photos, { onDelete: 'CASCADE' })
    queue: Queue;

    @Expose()
    get pathUrl(): string {
        if (!this.path) return null;

        // ถ้า path ขึ้นต้นด้วย http:// หรือ https:// ให้ return path เลย
        if (this.path.startsWith('http://') || this.path.startsWith('https://')) {
            return this.path;
        }

        // ถ้าไม่ใช่ URL สมบูรณ์ ให้ต่อ base URL เข้าไป
        return `${process.env.APP_URL}/${this.path}`;
    }
}