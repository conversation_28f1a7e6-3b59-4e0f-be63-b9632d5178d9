import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsNotEmpty, IsOptional, IsString, IsEnum } from 'class-validator';
import { TransactionType } from '../entities/inventory-transaction.entity';

export class InventoryAdjustmentDto {
    @ApiProperty({ description: 'รหัส Inventory' })
    @IsNumber()
    @IsNotEmpty()
    inventoryId: number;

    @ApiProperty({ description: 'ประเภทการปรับปรุง', enum: TransactionType })
    @IsEnum(TransactionType)
    type: TransactionType;

    @ApiProperty({ description: 'จำนวนที่เปลี่ยนแปลง (+/-)' })
    @IsNumber()
    @IsNotEmpty()
    quantity: number;

    @ApiProperty({ description: 'ต้นทุนต่อหน่วย', required: false })
    @IsOptional()
    @IsNumber()
    unitCost?: number;

    @ApiProperty({ description: 'รายละเอียด', required: false })
    @IsOptional()
    @IsString()
    description?: string;

    @ApiProperty({ description: 'หมายเหตุ', required: false })
    @IsOptional()
    @IsString()
    notes?: string;

    @ApiProperty({ description: 'ประเภทเอกสารอ้างอิง', required: false })
    @IsOptional()
    @IsString()
    referenceType?: string;

    @ApiProperty({ description: 'รหัสเอกสารอ้างอิง', required: false })
    @IsOptional()
    @IsNumber()
    referenceId?: number;

    @ApiProperty({ description: 'เลขที่เอกสารอ้างอิง', required: false })
    @IsOptional()
    @IsString()
    referenceNumber?: string;
}
